apiVersion: v1
kind: ConfigMap
metadata:
  name: nestjs-config
data:
  PORT: "8080"
  NODE_ENV: "production"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "userauth"
  JWT_EXPIRATION: "86400000"
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: '"No Reply" <<EMAIL>>'
  APP_URL: "http://localhost:3000"
  API_URL: "http://localhost:3000"
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
  GOOGLE_REDIRECT_URI: "http://localhost:8080/oauth2/callback/google"
