#!/bin/bash

# GitOps Deployment Verification Script for AI Nest Backend
# This script verifies that the Argo CD deployment is working correctly

set -e

echo "🚀 AI Nest Backend - GitOps Deployment Verification"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_status "ERROR" "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if minikube is running
if ! minikube status &> /dev/null; then
    print_status "ERROR" "Minikube is not running. Please start minikube first."
    exit 1
fi

print_status "SUCCESS" "Minikube is running"

# Check if Argo CD is installed
if ! kubectl get namespace argocd &> /dev/null; then
    print_status "ERROR" "Argo CD namespace not found. Please install Argo CD first."
    exit 1
fi

print_status "SUCCESS" "Argo CD namespace found"

# Check if Argo CD application exists
echo ""
print_status "INFO" "Checking Argo CD Application..."
if kubectl get application ai-nest-backend -n argocd &> /dev/null; then
    print_status "SUCCESS" "Argo CD Application 'ai-nest-backend' found"
    
    # Get application status
    APP_STATUS=$(kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}')
    APP_HEALTH=$(kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.health.status}')
    
    echo "  Sync Status: $APP_STATUS"
    echo "  Health Status: $APP_HEALTH"
    
    if [ "$APP_STATUS" = "Synced" ]; then
        print_status "SUCCESS" "Application is synced"
    else
        print_status "WARNING" "Application sync status: $APP_STATUS"
    fi
    
    if [ "$APP_HEALTH" = "Healthy" ]; then
        print_status "SUCCESS" "Application is healthy"
    else
        print_status "WARNING" "Application health status: $APP_HEALTH"
    fi
else
    print_status "ERROR" "Argo CD Application 'ai-nest-backend' not found"
    echo "To create the application, run: kubectl apply -f argocd-app.yaml"
    exit 1
fi

# Check pods
echo ""
print_status "INFO" "Checking Pod Status..."

# Check PostgreSQL pod
POSTGRES_POD=$(kubectl get pods -l app=postgres -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
if [ -n "$POSTGRES_POD" ]; then
    POSTGRES_STATUS=$(kubectl get pod $POSTGRES_POD -o jsonpath='{.status.phase}')
    if [ "$POSTGRES_STATUS" = "Running" ]; then
        print_status "SUCCESS" "PostgreSQL pod is running"
    else
        print_status "ERROR" "PostgreSQL pod status: $POSTGRES_STATUS"
    fi
else
    print_status "ERROR" "PostgreSQL pod not found"
fi

# Check NestJS pods
NESTJS_PODS=$(kubectl get pods -l app=nestjs-backend -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")
if [ -n "$NESTJS_PODS" ]; then
    RUNNING_COUNT=0
    TOTAL_COUNT=0
    for pod in $NESTJS_PODS; do
        TOTAL_COUNT=$((TOTAL_COUNT + 1))
        POD_STATUS=$(kubectl get pod $pod -o jsonpath='{.status.phase}')
        if [ "$POD_STATUS" = "Running" ]; then
            RUNNING_COUNT=$((RUNNING_COUNT + 1))
        fi
    done
    
    if [ $RUNNING_COUNT -eq $TOTAL_COUNT ]; then
        print_status "SUCCESS" "All NestJS pods are running ($RUNNING_COUNT/$TOTAL_COUNT)"
    else
        print_status "WARNING" "NestJS pods running: $RUNNING_COUNT/$TOTAL_COUNT"
    fi
else
    print_status "ERROR" "NestJS pods not found"
fi

# Check services
echo ""
print_status "INFO" "Checking Services..."

if kubectl get service nestjs-service &> /dev/null; then
    print_status "SUCCESS" "NestJS service found"
    NODEPORT=$(kubectl get service nestjs-service -o jsonpath='{.spec.ports[0].nodePort}')
    echo "  NodePort: $NODEPORT"
else
    print_status "ERROR" "NestJS service not found"
fi

if kubectl get service postgres-service &> /dev/null; then
    print_status "SUCCESS" "PostgreSQL service found"
else
    print_status "ERROR" "PostgreSQL service not found"
fi

# Check persistent volume claims
echo ""
print_status "INFO" "Checking Persistent Volume Claims..."

if kubectl get pvc postgres-pvc &> /dev/null; then
    PVC_STATUS=$(kubectl get pvc postgres-pvc -o jsonpath='{.status.phase}')
    if [ "$PVC_STATUS" = "Bound" ]; then
        print_status "SUCCESS" "PostgreSQL PVC is bound"
    else
        print_status "WARNING" "PostgreSQL PVC status: $PVC_STATUS"
    fi
else
    print_status "ERROR" "PostgreSQL PVC not found"
fi

# Test application connectivity
echo ""
print_status "INFO" "Testing Application Connectivity..."

# Get minikube IP and service URL
MINIKUBE_IP=$(minikube ip)
SERVICE_URL="http://$MINIKUBE_IP:$NODEPORT"

echo "  Service URL: $SERVICE_URL"

# Test health endpoint
if curl -s --max-time 10 "$SERVICE_URL/api/v1/oauth2/status" > /dev/null; then
    print_status "SUCCESS" "Application health endpoint is responding"
else
    print_status "WARNING" "Application health endpoint is not responding (this may be normal if the app is still starting)"
fi

# Summary
echo ""
echo "=================================================="
print_status "INFO" "Deployment Verification Summary"
echo "=================================================="

echo "🔗 Access URLs:"
echo "  Application: $SERVICE_URL"
echo "  API Docs: $SERVICE_URL/api-docs"
echo "  OAuth Status: $SERVICE_URL/api/v1/oauth2/status"
echo "  Email Status: $SERVICE_URL/api/v1/email/status"

echo ""
echo "📋 Useful Commands:"
echo "  View Argo CD app: kubectl get application ai-nest-backend -n argocd"
echo "  Check pods: kubectl get pods"
echo "  Check logs: kubectl logs -l app=nestjs-backend"
echo "  Access service: minikube service nestjs-service --url"
echo "  Force sync: kubectl patch application ai-nest-backend -n argocd -p '{\"operation\":{\"initiatedBy\":{\"username\":\"admin\"},\"sync\":{\"revision\":\"HEAD\"}}}' --type merge"

echo ""
print_status "SUCCESS" "Verification completed! Check the status messages above for any issues."
