# PostgreSQL Secret
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
type: Opaque
data:
  # Base64 encoded values - actual values from .env.development
  # echo -n "postgres" | base64
  username: cG9zdGdyZXM=
  # echo -n "password" | base64
  password: cGFzc3dvcmQ=
---
# NestJS Application Secret
apiVersion: v1
kind: Secret
metadata:
  name: nestjs-secret
type: Opaque
data:
  # Base64 encoded values - actual values from .env.development
  # echo -n "supersecretkey" | base64
  jwt-secret: c3VwZXJzZWNyZXRrZXk=
  # echo -n "<EMAIL>" | base64
  smtp-user: ****************************************
  # echo -n "fqactehafmzlltzz" | base64
  smtp-pass: ZnFhY3RlaGFmbXpsbHR6eg==
  # echo -n "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com" | base64
  google-client-id: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  # echo -n "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" | base64
  google-client-secret: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
