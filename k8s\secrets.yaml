# PostgreSQL Secret
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  # echo -n "postgres" | base64
  username: cG9zdGdyZXM=
  # echo -n "password" | base64  
  password: cGFzc3dvcmQ=
---
# NestJS Application Secret
apiVersion: v1
kind: Secret
metadata:
  name: nestjs-secret
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  # echo -n "supersecretkey" | base64
  jwt-secret: c3VwZXJzZWNyZXRrZXk=
  # echo -n "<EMAIL>" | base64
  smtp-user: ****************************************
  # echo -n "fqactehafmzlltzz" | base64
  smtp-pass: ZnFhY3RlaGFmbXpsbHR6eg==
  # echo -n "your-google-client-id" | base64
  google-client-id: eW91ci1nb29nbGUtY2xpZW50LWlk
  # echo -n "your-google-client-secret" | base64
  google-client-secret: eW91ci1nb29nbGUtY2xpZW50LXNlY3JldA==
