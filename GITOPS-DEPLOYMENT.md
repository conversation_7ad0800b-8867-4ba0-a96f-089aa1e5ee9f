# GitOps Deployment Guide: AI Nest Backend with Argo CD

## Overview

This guide provides a complete GitOps deployment pipeline for the ai-nest-backend NestJS application using Argo CD on Minikube. The setup includes automated deployment triggered by CI/CD pipeline updates.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GitHub Repo   │    │   Docker Hub    │    │   Minikube      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Source Code │ │    │ │ Docker      │ │    │ │ Argo CD     │ │
│ │ K8s Manifests│ │───▶│ │ Images      │ │    │ │ Application │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    │ ┌─────────────┐ │
                                              │ │ NestJS App  │ │
                                              │ │ PostgreSQL  │ │
                                              │ └─────────────┘ │
                                              └─────────────────┘
```

## Components

### 1. Application Stack
- **NestJS Backend**: 2 replicas with health checks and resource limits
- **PostgreSQL Database**: Single instance with persistent storage
- **Services**: NodePort (30000) for external access, ClusterIP for internal communication

### 2. Configuration Management
- **ConfigMaps**: Non-sensitive environment variables
- **Secrets**: Database credentials, JWT secrets, SMTP credentials, OAuth credentials
- **Persistent Volumes**: Database data persistence

### 3. GitOps Pipeline
- **Source**: GitHub repository with Kubernetes manifests
- **Deployment**: Argo CD with automated sync and self-healing
- **Monitoring**: Health checks and readiness probes

## Prerequisites

1. **Minikube** cluster running locally
2. **Argo CD** installed in the `argocd` namespace
3. **Docker image** available at `docker.io/saipriya104/ai-nest-backend:latest`
4. **kubectl** configured to access your Minikube cluster

### Installing Argo CD (if not already installed)

```bash
# Create namespace
kubectl create namespace argocd

# Install Argo CD
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml

# Wait for pods to be ready
kubectl wait --for=condition=available --timeout=300s deployment/argocd-server -n argocd

# Access Argo CD UI (optional)
kubectl port-forward svc/argocd-server -n argocd 8080:443
```

## Deployment Steps

### Step 1: Update Secrets

**IMPORTANT**: Before deploying, update the secrets in `k8s/secrets.yaml` with your actual base64-encoded values:

```bash
# Navigate to the repository root
cd /path/to/ai-nest-backend

# Encode your actual secrets
echo -n "your-actual-jwt-secret" | base64
echo -n "your-smtp-password" | base64
echo -n "your-google-client-id" | base64
echo -n "your-google-client-secret" | base64

# Update k8s/secrets.yaml with the encoded values
```

### Step 2: Deploy Using Scripts

Use the provided deployment script:

```bash
# Make scripts executable (if not already)
chmod +x scripts/deploy.sh scripts/verify-deployment.sh

# Run deployment
./scripts/deploy.sh
```

### Step 3: Manual Deployment (Alternative)

If you prefer manual deployment:

```bash
# Apply Argo CD Application
kubectl apply -f argocd-app.yaml

# Wait for sync
kubectl get application ai-nest-backend -n argocd -w
```

### Step 4: Verify Deployment

```bash
# Run verification script
./scripts/verify-deployment.sh

# Or manually check
kubectl get pods
kubectl get services
kubectl get application ai-nest-backend -n argocd
```

## Access Information

### Application URLs

```bash
# Get service URL
minikube service nestjs-service --url

# Or construct manually
MINIKUBE_IP=$(minikube ip)
echo "Application: http://$MINIKUBE_IP:30000"
echo "API Docs: http://$MINIKUBE_IP:30000/api-docs"
echo "OAuth Status: http://$MINIKUBE_IP:30000/api/v1/oauth2/status"
```

### Health Check Endpoints

- **OAuth Status**: `/api/v1/oauth2/status`
- **Email Status**: `/api/v1/email/status`
- **API Documentation**: `/api-docs`

## Configuration Details

### Environment Variables

#### ConfigMap (Non-sensitive)
```yaml
PORT: "8080"
NODE_ENV: "production"
DB_HOST: "postgres-service"
DB_PORT: "5432"
DB_NAME: "userauth"
CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
# ... and more
```

#### Secrets (Sensitive)
```yaml
# Base64 encoded values
jwt-secret: <base64-encoded-jwt-secret>
smtp-user: <base64-encoded-smtp-user>
smtp-pass: <base64-encoded-smtp-password>
google-client-id: <base64-encoded-google-client-id>
google-client-secret: <base64-encoded-google-client-secret>
```

### Resource Limits

#### NestJS Application
- **Requests**: 256Mi memory, 250m CPU
- **Limits**: 512Mi memory, 500m CPU

#### PostgreSQL Database
- **Requests**: 128Mi memory, 125m CPU
- **Limits**: 256Mi memory, 250m CPU

## Troubleshooting

### Common Issues

1. **Application Not Starting**
   ```bash
   # Check pod logs
   kubectl logs -l app=nestjs-backend
   
   # Check events
   kubectl get events --sort-by=.metadata.creationTimestamp
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   kubectl get pods -l app=postgres
   kubectl logs -l app=postgres
   
   # Verify service
   kubectl get svc postgres-service
   ```

3. **Argo CD Sync Issues**
   ```bash
   # Check application status
   kubectl describe application ai-nest-backend -n argocd
   
   # Force sync
   kubectl patch application ai-nest-backend -n argocd -p '{"operation":{"initiatedBy":{"username":"admin"},"sync":{"revision":"HEAD"}}}' --type merge
   ```

### Useful Commands

```bash
# View all resources
kubectl get all

# Check Argo CD application
kubectl get application ai-nest-backend -n argocd -o yaml

# Scale application
kubectl scale deployment nestjs-backend --replicas=3

# Update image (triggers new deployment)
kubectl set image deployment/nestjs-backend nestjs-backend=docker.io/saipriya104/ai-nest-backend:v1.1.0

# Check resource usage
kubectl top pods
```

## CI/CD Integration

The current CI pipeline (`.github/workflows/ci.yml`) builds and pushes Docker images. To complete the GitOps loop:

1. **Image Updates**: Argo CD can be configured with Image Updater to automatically update image tags
2. **Manifest Updates**: CI pipeline can update Kubernetes manifests and commit changes
3. **Notifications**: Configure Argo CD to send notifications on deployment status

### Optional: Argo CD Image Updater

```bash
# Install Argo CD Image Updater
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj-labs/argocd-image-updater/stable/manifests/install.yaml

# Configure image updater annotations in deployment
```

## Security Considerations

1. **Secrets Management**: Use external secret management (e.g., Sealed Secrets, External Secrets Operator)
2. **Network Policies**: Implement network policies to restrict pod-to-pod communication
3. **RBAC**: Configure proper role-based access control
4. **Image Security**: Use specific image tags and scan images for vulnerabilities

## Monitoring and Observability

Consider adding:

1. **Prometheus**: Metrics collection
2. **Grafana**: Dashboards and visualization
3. **Jaeger**: Distributed tracing
4. **ELK Stack**: Log aggregation and analysis

## Production Considerations

1. **High Availability**: Deploy PostgreSQL with replication
2. **Backup Strategy**: Implement database backup and restore procedures
3. **Resource Scaling**: Configure Horizontal Pod Autoscaler (HPA)
4. **Ingress**: Use Ingress controller instead of NodePort for production
5. **TLS**: Configure TLS certificates for secure communication

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Argo CD documentation: https://argo-cd.readthedocs.io/
3. Check application logs and Kubernetes events
4. Verify all prerequisites are met
