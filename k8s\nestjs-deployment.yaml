apiVersion: apps/v1
kind: Deployment
metadata:
  name: nestjs-backend
  labels:
    app: nestjs-backend
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nestjs-backend
  template:
    metadata:
      labels:
        app: nestjs-backend
        version: v1
    spec:
      containers:
      - name: nestjs-backend
        image: docker.io/saipriya104/ai-nest-backend:latest
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: nestjs-config
        env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: nestjs-secret
              key: jwt-secret
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: nestjs-secret
              key: smtp-user
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: nestjs-secret
              key: smtp-pass
        - name: GOO<PERSON><PERSON>_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: nestjs-secret
              key: google-client-id
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: nestjs-secret
              key: google-client-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/oauth2/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/oauth2/status
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        imagePullPolicy: Never
      restartPolicy: Always
