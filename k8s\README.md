# Kubernetes Deployment for AI Nest Backend

This directory contains Kubernetes manifests for deploying the ai-nest-backend NestJS application with PostgreSQL database using GitOps with Argo CD.

## Prerequisites

1. **Minikube cluster** running locally
2. **Argo CD** installed in the `argocd` namespace
3. **Docker image** available at `docker.io/saipriya104/ai-nest-backend:latest`

## Architecture

- **NestJS Backend**: Deployed as a Deployment with 2 replicas
- **PostgreSQL Database**: Single instance with persistent storage
- **Services**: NodePort for external access, ClusterIP for internal communication
- **Configuration**: ConfigMaps for non-sensitive data, Secrets for sensitive data

## Files Overview

- `nestjs-deployment.yaml` - NestJS application deployment
- `nestjs-service.yaml` - Service for NestJS application (NodePort on 30000)
- `postgres-deployment.yaml` - PostgreSQL database deployment
- `postgres-service.yaml` - Service for PostgreSQL (ClusterIP)
- `postgres-pvc.yaml` - Persistent Volume Claim for database storage
- `configmap.yaml` - Non-sensitive configuration
- `secrets.yaml` - Sensitive configuration (needs to be updated with real values)
- `kustomization.yaml` - Kustomize configuration for resource management

## Deployment Steps

### 1. Update Secrets (IMPORTANT)

Before deploying, update the `secrets.yaml` file with your actual base64-encoded values:

```bash
# Encode your actual values
echo -n "your-actual-jwt-secret" | base64
echo -n "your-smtp-password" | base64
echo -n "your-google-client-id" | base64
echo -n "your-google-client-secret" | base64
```

### 2. Deploy with Argo CD

Apply the Argo CD Application from the repository root:

```bash
kubectl apply -f argocd-app.yaml
```

### 3. Verify Deployment

Check Argo CD application status:
```bash
kubectl get applications -n argocd
kubectl describe application ai-nest-backend -n argocd
```

Check pods and services:
```bash
kubectl get pods
kubectl get services
kubectl get pvc
```

### 4. Access the Application

Get the Minikube service URL:
```bash
minikube service nestjs-service --url
```

Or access via NodePort:
```bash
# Get Minikube IP
minikube ip
# Access application at http://<minikube-ip>:30000
```

## Health Checks

The application includes health checks on the `/api/v1/oauth2/status` endpoint:
- **Liveness Probe**: Checks if the application is running
- **Readiness Probe**: Checks if the application is ready to serve traffic

## Environment Variables

### ConfigMap (Non-sensitive)
- PORT, NODE_ENV, DB_HOST, DB_PORT, DB_NAME
- CORS settings, SMTP host/port, redirect URIs

### Secrets (Sensitive)
- Database credentials (DB_USER, DB_PASSWORD)
- JWT secret
- SMTP credentials
- Google OAuth credentials

## Troubleshooting

### Check Application Logs
```bash
kubectl logs -l app=nestjs-backend
kubectl logs -l app=postgres
```

### Check Service Endpoints
```bash
kubectl get endpoints
```

### Force Argo CD Sync
```bash
kubectl patch application ai-nest-backend -n argocd -p '{"operation":{"initiatedBy":{"username":"admin"},"sync":{"revision":"HEAD"}}}' --type merge
```

### Database Connection Issues
1. Verify PostgreSQL is running: `kubectl get pods -l app=postgres`
2. Check PostgreSQL logs: `kubectl logs -l app=postgres`
3. Verify service connectivity: `kubectl get svc postgres-service`

### Application Startup Issues
1. Check environment variables: `kubectl describe pod <nestjs-pod-name>`
2. Verify secrets are properly mounted: `kubectl get secrets`
3. Check application logs for specific errors

## Scaling

Scale the NestJS application:
```bash
kubectl scale deployment nestjs-backend --replicas=3
```

## Updates

The GitOps pipeline automatically syncs changes when:
1. New Docker images are pushed to `docker.io/saipriya104/ai-nest-backend:latest`
2. Kubernetes manifests are updated in the repository
3. Argo CD detects changes and applies them automatically

## Security Notes

1. **Secrets**: Update all default passwords and secrets before production use
2. **Network Policies**: Consider implementing network policies for production
3. **RBAC**: Configure proper RBAC for service accounts
4. **Image Security**: Use specific image tags instead of `latest` for production

## Monitoring

Consider adding:
- Prometheus metrics collection
- Grafana dashboards
- Log aggregation with ELK stack
- Application performance monitoring
