#!/bin/bash

# GitOps Deployment Script for AI Nest Backend
# This script deploys the application using Argo CD

set -e

echo "🚀 AI Nest Backend - GitOps Deployment"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check prerequisites
print_status "INFO" "Checking prerequisites..."

if ! command -v kubectl &> /dev/null; then
    print_status "ERROR" "kubectl is not installed or not in PATH"
    exit 1
fi

if ! minikube status &> /dev/null; then
    print_status "ERROR" "Minikube is not running. Please start minikube first."
    exit 1
fi

if ! kubectl get namespace argocd &> /dev/null; then
    print_status "ERROR" "Argo CD namespace not found. Please install Argo CD first."
    exit 1
fi

print_status "SUCCESS" "All prerequisites met"

# Check if secrets need to be updated
print_status "WARNING" "IMPORTANT: Make sure to update secrets in k8s/secrets.yaml with your actual values!"
echo "  - JWT_SECRET"
echo "  - SMTP credentials"
echo "  - Google OAuth credentials"
echo "  - Database password (if different from default)"
echo ""

read -p "Have you updated the secrets? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "WARNING" "Please update the secrets in k8s/secrets.yaml before deploying"
    echo "Example commands to encode secrets:"
    echo "  echo -n 'your-jwt-secret' | base64"
    echo "  echo -n 'your-smtp-password' | base64"
    echo "  echo -n 'your-google-client-id' | base64"
    echo "  echo -n 'your-google-client-secret' | base64"
    exit 1
fi

# Deploy Argo CD Application
print_status "INFO" "Deploying Argo CD Application..."

if kubectl apply -f argocd-app.yaml; then
    print_status "SUCCESS" "Argo CD Application created/updated"
else
    print_status "ERROR" "Failed to create Argo CD Application"
    exit 1
fi

# Wait for application to be created
print_status "INFO" "Waiting for Argo CD to sync the application..."
sleep 5

# Check application status
for i in {1..12}; do
    if kubectl get application ai-nest-backend -n argocd &> /dev/null; then
        APP_STATUS=$(kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}' 2>/dev/null || echo "Unknown")
        print_status "INFO" "Application sync status: $APP_STATUS (attempt $i/12)"
        
        if [ "$APP_STATUS" = "Synced" ]; then
            print_status "SUCCESS" "Application synced successfully!"
            break
        fi
        
        if [ $i -eq 12 ]; then
            print_status "WARNING" "Application sync is taking longer than expected"
            print_status "INFO" "You can check the status manually with: kubectl get application ai-nest-backend -n argocd"
        fi
    else
        print_status "INFO" "Waiting for application to be created... (attempt $i/12)"
    fi
    
    sleep 10
done

# Wait for pods to be ready
print_status "INFO" "Waiting for pods to be ready..."
sleep 10

# Check pod status
for i in {1..18}; do
    POSTGRES_READY=false
    NESTJS_READY=false
    
    # Check PostgreSQL
    if kubectl get pods -l app=postgres &> /dev/null; then
        POSTGRES_STATUS=$(kubectl get pods -l app=postgres -o jsonpath='{.items[0].status.phase}' 2>/dev/null || echo "Unknown")
        if [ "$POSTGRES_STATUS" = "Running" ]; then
            POSTGRES_READY=true
        fi
    fi
    
    # Check NestJS
    NESTJS_RUNNING=$(kubectl get pods -l app=nestjs-backend -o jsonpath='{.items[?(@.status.phase=="Running")].metadata.name}' 2>/dev/null | wc -w)
    NESTJS_TOTAL=$(kubectl get pods -l app=nestjs-backend -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | wc -w)
    
    if [ "$NESTJS_RUNNING" -gt 0 ] && [ "$NESTJS_RUNNING" -eq "$NESTJS_TOTAL" ]; then
        NESTJS_READY=true
    fi
    
    print_status "INFO" "Pod status check (attempt $i/18): PostgreSQL=$POSTGRES_READY, NestJS=$NESTJS_READY ($NESTJS_RUNNING/$NESTJS_TOTAL)"
    
    if [ "$POSTGRES_READY" = true ] && [ "$NESTJS_READY" = true ]; then
        print_status "SUCCESS" "All pods are ready!"
        break
    fi
    
    if [ $i -eq 18 ]; then
        print_status "WARNING" "Some pods are not ready yet. This may be normal for first deployment."
    fi
    
    sleep 10
done

# Get service information
print_status "INFO" "Getting service information..."

if kubectl get service nestjs-service &> /dev/null; then
    NODEPORT=$(kubectl get service nestjs-service -o jsonpath='{.spec.ports[0].nodePort}')
    MINIKUBE_IP=$(minikube ip)
    SERVICE_URL="http://$MINIKUBE_IP:$NODEPORT"
    
    print_status "SUCCESS" "Service is available at: $SERVICE_URL"
else
    print_status "ERROR" "NestJS service not found"
fi

# Final summary
echo ""
echo "======================================"
print_status "SUCCESS" "Deployment completed!"
echo "======================================"

echo ""
echo "🔗 Access Information:"
echo "  Application URL: $SERVICE_URL"
echo "  API Documentation: $SERVICE_URL/api-docs"
echo "  OAuth Status: $SERVICE_URL/api/v1/oauth2/status"
echo "  Email Status: $SERVICE_URL/api/v1/email/status"

echo ""
echo "📋 Next Steps:"
echo "  1. Test the application: curl $SERVICE_URL/api/v1/oauth2/status"
echo "  2. Run verification script: ./scripts/verify-deployment.sh"
echo "  3. Check Argo CD UI for detailed status"
echo "  4. Monitor logs: kubectl logs -l app=nestjs-backend"

echo ""
echo "🔧 Useful Commands:"
echo "  Check application status: kubectl get application ai-nest-backend -n argocd"
echo "  View pods: kubectl get pods"
echo "  Access service: minikube service nestjs-service --url"
echo "  Force sync: kubectl patch application ai-nest-backend -n argocd -p '{\"operation\":{\"initiatedBy\":{\"username\":\"admin\"},\"sync\":{\"revision\":\"HEAD\"}}}' --type merge"

print_status "INFO" "Deployment script completed successfully!"
